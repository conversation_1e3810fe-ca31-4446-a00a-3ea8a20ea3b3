import React, { useState } from 'react';
import './App.css';

const keysA = ['1A', '2A', '3A', '4A', '5A', '6A', '7A', '8A', '9A', '10A', '11A', '12A'];
const keysB = ['1B', '2B', '3B', '4B', '5B', '6B', '7B', '8B', '9B', '10B', '11B', '12B'];

function getMixingOptions(key) {
  const num = parseInt(key.slice(0, -1));
  const mode = key.slice(-1);
  const wrapKey = (val, m) => `${((val - 1 + 12) % 12 + 1)}${m}`;

  return {
    'Perfect Mix': [key],
    'Adjacent Keys': [wrapKey(num - 1, mode), wrapKey(num + 1, mode)],
    'Relative Key': [`${num}${mode === 'A' ? 'B' : 'A'}`],
    'Energy Boost': [wrapKey(num + 2, mode)],
    'Energy Drop': [wrapKey(num - 2, mode)],
    'Creative Options': [wrapKey(num - 1, mode === 'A' ? 'B' : 'A'), wrapKey(num + 1, mode === 'A' ? 'B' : 'A')]
  };
}

const KeyButton = ({ label, index, radius, color, onClick, isSelected }) => {
  const angle = (2 * Math.PI * index) / 12 - Math.PI / 2;
  const x = Math.cos(angle) * radius;
  const y = Math.sin(angle) * radius;
  const style = {
    transform: `translate(${x}px, ${y}px)`
  };

  return (
    <div 
      className={`key-button ${isSelected ? 'selected' : ''}`} 
      style={style} 
      onClick={() => onClick(label)}
    >
      <div className="circle" style={{ backgroundColor: color }}>
        <span className="key-label">{label}</span>
      </div>
    </div>
  );
};

function App() {
  const [selectedKey, setSelectedKey] = useState(null);

  const handleKeyClick = (key) => setSelectedKey(key);

  const renderDetails = (key) => {
    if (!key) return null;
    const options = getMixingOptions(key);
    return (
      <div className="details">
        <div className="details-header">
          <h3>Mixing Options for <span className="selected-key">{key}</span></h3>
          <button className="close-btn" onClick={() => setSelectedKey(null)}>×</button>
        </div>
        <div className="mixing-options">
          {Object.entries(options).map(([label, values]) => (
            <div key={label} className="option-row">
              <span className="option-label">{label}:</span>
              <div className="option-values">
                {values.map((value, index) => (
                  <span key={index} className="key-tag">{value}</span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1 className="app-title">Harmonic KeyMix</h1>
        <p className="app-subtitle">Professional DJ Mixing Assistant</p>
      </header>
      
      <main className="main-content">
        <div className="wheel-container">
          <div className="wheel">
            <div className="wheel-center">
              <div className="center-logo">
                <span className="logo-text">CAMELOT</span>
                <span className="logo-subtext">WHEEL</span>
              </div>
            </div>
            
            {keysA.map((key, i) => (
              <KeyButton
                key={key}
                label={key}
                index={i}
                radius={160}
                color="#FF6B35"
                onClick={handleKeyClick}
                isSelected={selectedKey === key}
              />
            ))}
            
            {keysB.map((key, i) => (
              <KeyButton
                key={key}
                label={key}
                index={i}
                radius={100}
                color="#00D4FF"
                onClick={handleKeyClick}
                isSelected={selectedKey === key}
              />
            ))}
          </div>
          
          <div className="wheel-legend">
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#FF6B35' }}></div>
              <span>Major Keys (A)</span>
            </div>
            <div className="legend-item">
              <div className="legend-color" style={{ backgroundColor: '#00D4FF' }}></div>
              <span>Minor Keys (B)</span>
            </div>
          </div>
        </div>
        
        {renderDetails(selectedKey)}
      </main>
    </div>
  );
}

export default App;

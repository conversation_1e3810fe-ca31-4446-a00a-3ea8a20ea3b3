.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  padding: 20px;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;
  animation: fadeInDown 0.8s ease-out;
}

.app-title {
  font-family: 'Orbitron', monospace;
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(45deg, #FF6B35, #00D4FF, #FF6B35);
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease-in-out infinite;
  margin-bottom: 10px;
  text-shadow: 0 0 30px rgba(255, 107, 53, 0.3);
}

.app-subtitle {
  font-family: 'Inter', sans-serif;
  font-size: 1.2rem;
  font-weight: 300;
  color: #a0a0a0;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  width: 100%;
  max-width: 1200px;
}

.wheel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.wheel {
  position: relative;
  width: 400px;
  height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 1s ease-out;
}

.wheel-center {
  position: absolute;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2a2a3e, #1a1a2e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #444;
  box-shadow: 
    0 0 20px rgba(0, 212, 255, 0.3),
    inset 0 0 20px rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.center-logo {
  text-align: center;
  font-family: 'Orbitron', monospace;
}

.logo-text {
  display: block;
  font-size: 0.7rem;
  font-weight: 700;
  color: #00D4FF;
  line-height: 1;
}

.logo-subtext {
  display: block;
  font-size: 0.5rem;
  font-weight: 400;
  color: #888;
  line-height: 1;
}

.key-button {
  position: absolute;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 5;
}

.key-button:hover {
  transform: translate(var(--x), var(--y)) scale(1.1);
  z-index: 15;
}

.key-button.selected {
  z-index: 20;
  animation: pulse 1.5s ease-in-out infinite;
}

.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.key-button:hover .circle {
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 30px currentColor,
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

.key-label {
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.wheel-legend {
  display: flex;
  gap: 30px;
  font-family: 'Inter', sans-serif;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.details {
  background: linear-gradient(135deg, rgba(42, 42, 62, 0.9), rgba(26, 26, 46, 0.9));
  border-radius: 20px;
  padding: 30px;
  width: 100%;
  max-width: 600px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 50px rgba(0, 212, 255, 0.1);
  backdrop-filter: blur(20px);
  animation: slideUp 0.4s ease-out;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.details-header h3 {
  font-family: 'Orbitron', monospace;
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  margin: 0;
}

.selected-key {
  color: #00D4FF;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.close-btn {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.mixing-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-row {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.option-label {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  color: #00D4FF;
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.option-values {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.key-tag {
  background: linear-gradient(135deg, #FF6B35, #FF8A65);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 25px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 0.85rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
  transition: all 0.2s ease;
  cursor: pointer;
}

.key-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0%, 100% { transform: translate(var(--x), var(--y)) scale(1); }
  50% { transform: translate(var(--x), var(--y)) scale(1.15); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 15px;
  }
  
  .app-title {
    font-size: 2.2rem;
  }
  
  .wheel {
    width: 320px;
    height: 320px;
  }
  
  .key-button {
    transform: scale(0.8);
  }
  
  .details {
    padding: 20px;
    margin: 0 10px;
  }
  
  .wheel-legend {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
